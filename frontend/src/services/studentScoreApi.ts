import { ApiResponse,PaginatedApiResponse } from '@/types';
import apiClient from './apiClient';

export interface HomeworkList {
  student_id: string;
  page_params: PageParams;
}

export interface PageParams {
  page: number;
  page_size: number;
}

export interface HomeworkListResponse {
  id: string;
  homework_name: string;
  homework_status: string;
  subject_group_id: string;
  description: string;
  leaf_count: number;
  leaf_total: number;
  page_count: number;
  page_total: number;
  created_at: Date;
  updated_at: Date;
}

export const studentScoreApi = {
  // 获取学生端作业列表
  getStudentHomeworks: async (tenant_name: string, params: HomeworkList): Promise<PaginatedApiResponse<HomeworkListResponse>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homework/pageStudentHomeworks`, params);
  },

  // 提交反馈
  submitFeedback: async (tenant_name: string, params: {
    homework_id: string,
    student_id: string,
    score_id?: string,
    text: string,
  }): Promise<ApiResponse<any>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homework/feedback/create`, params);
  },
};
export default studentScoreApi;
