use crate::model::homework_students::homework_students::{HomeworkStudentStatus, HomeworkStudents};
use crate::model::Student;
use bigdecimal::BigDecimal;
use serde::Serialize;
use sqlx::{pool::PoolConnection, PgPool, Postgres};
use tracing::debug;
use uuid::Uuid;

/**
 * 作者：张瀚
 * 说明：作业和学生关联表的数据库方法
 */
pub struct HomeworkStudentsRepository {}

impl HomeworkStudentsRepository {
    /**
     * 作者：张瀚
     * 说明：通过作业ID查询涉及的学生列表
     */
    pub async fn find_student_list_by_homework_id(conn: &mut PoolConnection<Postgres>, homework_id: &Uuid) -> Result<Vec<Student>, String> {
        sqlx::query_as::<_, Student>("SELECT s.* FROM students s ,homework_students hs WHERE hs.homework_id = $1 AND s.id = hs.student_id ORDER BY s.created_at DESC")
            .bind(homework_id)
            .fetch_all(conn.as_mut())
            .await
            .map_err(|e| e.to_string())
            .map(|data| data)
    }
    pub async fn fetch_class_ids_by_homework_id(db: &PgPool, schema_name: &str, homework_id: &Uuid) -> anyhow::Result<Vec<(Uuid, i64)>> {
        let mut builder = sqlx::QueryBuilder::new(format!("SELECT class_id, COUNT(*) FROM {}.homework_students WHERE homework_id = ", schema_name));
        builder.push_bind(homework_id).push(" GROUP BY class_id");
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }

    fn gen_builder_with_fetch_homework_students_by_homework_id<'a>(schema_name: &str, homework_id: Uuid, class_id: Option<Uuid>) -> sqlx::QueryBuilder<'a, Postgres> {
        let mut builder = sqlx::QueryBuilder::new(format!("SELECT * FROM {}.homework_students WHERE homework_id = ", schema_name));
        builder.push_bind(homework_id);
        if let Some(class_id) = class_id {
            builder.push(" AND class_id = ").push_bind(class_id);
        }
        builder.push(" order by created_at desc");
        builder
    }
    pub async fn fetch_homework_students_by_homework_id(db: &PgPool, schema_name: &str, homework_id: &Uuid, class_id: Option<Uuid>) -> Result<Vec<HomeworkStudents>, String> {
        let mut builder = Self::gen_builder_with_fetch_homework_students_by_homework_id(schema_name, *homework_id, class_id);
        builder.build_query_as().fetch_all(db).await.map_err(|e| e.to_string())
    }

    /// 通过作业ID和学生ID查询该学生是否参与这块信息
    pub async fn get_student_by_homework_id_and_student_id(conn: &mut PoolConnection<Postgres>, homework_id: &Uuid, student_id: &Uuid) -> Result<Option<Student>, String> {
        let query = " SELECT s.* FROM students s INNER JOIN homework_students hs ON s.id = hs.student_id WHERE 1=1 ";
        let mut builder = sqlx::QueryBuilder::new(query);

        builder
            .push(" AND hs.homework_id = ")
            .push_bind(homework_id)
            .push(" AND hs.student_id = ")
            .push_bind(student_id)
            .push(" ORDER BY s.created_at DESC ");
        debug!("{:?}", builder.sql());
        builder.build_query_as::<Student>().fetch_optional(conn.as_mut()).await.map_err(|e| e.to_string()).map(|data| data)
    }
    pub async fn update_homework_student_status(db: &PgPool, tenant_name: &str, student_id: Uuid, status: HomeworkStudentStatus) -> anyhow::Result<()> {
        let mut builder = sqlx::QueryBuilder::new(format!("UPDATE {}.homework_students SET status = ", tenant_name));
        builder.push_bind(status).push(" WHERE student_id = ").push_bind(student_id);
        builder.build().execute(db).await?;
        Ok(())
    }
    pub async fn fetch_homework_students_by_homework_class(db: &PgPool, tenant_name: &str, homework_id: Uuid, class_id: Option<Uuid>) -> anyhow::Result<Vec<HomeworkStudents>> {
        let mut builder = sqlx::QueryBuilder::new(format!("SELECT * FROM {}.homework_students WHERE homework_id = ", tenant_name));
        builder.push_bind(homework_id);
        if let Some(class_id) = class_id {
            builder.push(" AND class_id = ").push_bind(class_id);
        }
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }

    /// 根据班级ID列表获取作业ID列表
    ///
    /// 该函数通过查询指定租户下的homework_students表，获取与给定班级ID关联的所有作业ID，
    /// 并对结果进行去重处理。
    ///
    /// # 参数说明
    /// * `db` - PostgreSQL数据库连接池引用，用于执行数据库查询操作
    /// * [schema_name] - 租户域名称，用于构建表名前缀，确定查询的数据库模式
    /// * `class_ids` - 班级ID向量，包含需要查询作业的班级标识符列表
    ///
    /// # 返回值
    /// 返回Result包装的作业ID向量，成功时包含去重后的作业UUID列表，失败时返回错误信息
    ///
    /// # 错误处理
    /// 当租户域名称为空或班级ID列表为空时，函数会返回相应的错误信息
    pub async fn fetch_homework_ids_by_class(db: &PgPool, schema_name: &str, class_ids: Vec<Uuid>) -> anyhow::Result<Vec<Uuid>> {

        // 验证输入参数
        if schema_name.is_empty() {
            return Err(anyhow::anyhow!("租户域名称不能为空"));
        }

        if class_ids.is_empty() {
            return  Err(anyhow::anyhow!("班级ID不能为空"));
        }
        // 使用参数化查询避免SQL注入风险
        let query = format!(
            "SELECT DISTINCT hs.homework_id FROM {}.homework_students hs WHERE hs.class_id = ANY($1)",
            // 注意：这里仍然存在潜在风险，实际生产中应该对tenant_name进行白名单验证
            schema_name
        );

        let ret: Vec<(Uuid,)> = sqlx::query_as(&query)
            .bind(&class_ids)
            .fetch_all(db)
            .await?;

        Ok(ret.into_iter().map(|(id,)| id).collect())
    }

    pub async fn fetch_students_by_homework_class(db: &PgPool, tenant_name: &str, homework_id: Uuid, class_id: Option<Uuid>) -> anyhow::Result<Vec<HomeworkStudentBase>> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            r#"
            SELECT hs.student_id, s.student_number, s.student_name, hs.status  FROM {}.homework_students hs
            INNER JOIN {}.students s ON s.id = hs.student_id
            WHERE hs.homework_id = "#,
            tenant_name, tenant_name
        ));
        builder.push_bind(homework_id);
        if let Some(class_id) = class_id {
            builder.push(" AND hs.class_id = ").push_bind(class_id);
        }
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }
    pub async fn fetch_homework_student_summary(db: &PgPool, tenant_name: &str, homework_id: Uuid) -> anyhow::Result<Vec<HomeworkStudentSummary>> {
        let mut builder = sqlx::QueryBuilder::new(format!(r#"SELECT status, COUNT(*) as count FROM {}.homework_students WHERE homework_id = "#, tenant_name));
        builder.push_bind(homework_id).push(" GROUP BY status");
        let ret = builder.build_query_as().fetch_all(db).await?;
        Ok(ret)
    }

    /**
     * 作者：朱若彪
     * 作用：查出某学生的所有相关作业
     * 返回: homework_student
     */
    pub async fn fetch_homework_by_student_ids(conn: &mut PoolConnection<Postgres>, student_ids: &Vec<Uuid>, subject_group_id: Option<Uuid>,start_time: Option<i64>,end_time: Option<i64>) -> Result<Vec<HomeworkStudents>, String> {
        let mut builder = sqlx::QueryBuilder::new("SELECT hs.* FROM homework_students hs INNER JOIN homework h ON hs.homework_id = h.id WHERE hs.student_id = ANY(");
        // 2. 绑定学生ID列表（整个Vec作为一个参数绑定给ANY）
        builder.push_bind(student_ids);
        builder.push(")");
        if let (Some(start), Some(end)) = (start_time, end_time) {
            builder.push(" AND h.created_at >= to_timestamp(").push_bind(start).push(")");
            builder.push(" AND h.created_at <= to_timestamp(").push_bind(end).push(")");
        }
        if let Some(sg_id) = subject_group_id {
            builder.push(" AND h.subject_group_id = ");
            builder.push_bind(sg_id);
        }
        builder.push(" ORDER BY created_at DESC ");
        let res = builder.build_query_as().fetch_all(conn.as_mut()).await.map_err(|e| e.to_string())?;
        Ok(res)
    }
    
    pub async fn get_homework_student_by_id(conn: &mut PoolConnection<Postgres>, id: &Uuid) -> Result<Vec<HomeworkStudents>, String> {
        sqlx::query_as::<_, HomeworkStudents>("SELECT * FROM homework_students WHERE homework_id = $1")
            .bind(id)
            .fetch_all(conn.as_mut())
            .await
            .map_err(|e| e.to_string())
            .map(|data| data)
    }
    /**
     * 作者：朱若彪
     * 说明：插入学生作业分数
     */
    pub async fn update_homework_student_score(db: &PgPool, schema_name: &String, homework_id:&Uuid,student_id: &Uuid,scores: BigDecimal) -> anyhow::Result<()> {
        let mut builder = sqlx::QueryBuilder::new(format!("UPDATE {}.homework_students SET scores = ", schema_name));
        builder.push_bind(scores).push(" WHERE homework_id = ").push_bind(homework_id).push(" AND student_id = ").push_bind(student_id);
        builder.build().execute(db).await?;
        Ok(())
    }
}
#[derive(Debug, Serialize, sqlx::FromRow)]
pub struct HomeworkStudentSummary {
    pub count: i64,
    pub status: HomeworkStudentStatus,
}

#[derive(Debug, Clone, Serialize, sqlx::FromRow)]
pub struct HomeworkStudentBase {
    pub student_id: Uuid,
    pub student_number: String,
    pub student_name: String,
    pub status: HomeworkStudentStatus,
}
