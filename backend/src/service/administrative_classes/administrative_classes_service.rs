use std::collections::HashSet;

use sqlx::PgPool;
use uuid::Uuid;

use crate::{
    middleware::auth_middleware::AuthContext, model::{
        administrative_classes::administrative_classes::{
            AdministrativeClasses, AdministrativeClassesStatistics, CreateAdministrativeClassesParams, DeleteAdministrativeClassesParams, FindAllStudentInClassParams, MoveStudentToAdministrativeClassesParams, PageStudentInClassParams, PageUserClassListParams, RemoveStudentFromAdministrativeClassesParams, UpdateAdministrativeClassesParams
        },
        Student,
    }, repository::students::student_repository::StudentsRepository, utils::schema::connect_with_schema
};
use anyhow::{bail, Result};
use crate::model::auth::UserIdentity;

#[derive(Clone)]
pub struct AdministrativeClassesService {
    db_pool: PgPool,
}

//行政班级管理服务
impl AdministrativeClassesService {
    pub fn new(pool: PgPool) -> Self {
        Self { db_pool: pool }
    }

    /**
     * 作者：张瀚
     * 说明：获取用户能查看的行政班列表(不包含管理员权限)
     */
    pub async fn get_user_class_list(
        &self,
        schema_name: &String,
        user_id: &Uuid,
    ) -> Result<Vec<AdministrativeClasses>, String> {
        let mut result: Vec<AdministrativeClasses> = vec![];
        //自己担任班主任的班级列表
        let mut head_teacher_class_list = self
            .find_head_teacher_class_list(schema_name, user_id)
            .await?;
        result.append(&mut head_teacher_class_list);
        //自己担任临时班主任的班级列表 TODO
        Ok(result)
    }

    /**
     * 作者：张瀚
     * 说明：查询所有行政班（管理员功能）
     */
    pub async fn get_all_class_list(
        &self,
        schema_name: &String,
    ) -> Result<Vec<AdministrativeClasses>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.administrative_classes ac order by ac.is_active desc , ac.created_at desc ",
            schema_name
        ));
        builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
    }

    /**
     * 作者：朱若彪
     * 说明：分页查询班级列表(管理员功能)
     */
    pub async fn page_all_class_list(
        &self,
        schema_name: &String,
        params: &PageUserClassListParams,
    ) -> Result<(Vec<AdministrativeClasses>,i64), String> {
        let PageUserClassListParams { page_params, name_like, class_code, is_active } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.administrative_classes ac where 1=1 ",
            schema_name
        ));
        let mut count_builder = sqlx::QueryBuilder::new(format!(
            "SELECT count(*) from {}.administrative_classes ac where 1=1 ",
            schema_name
        ));
        if let Some(name_like) = name_like {
            builder.push(" and ac.class_name like ");
            builder.push_bind(format!("%{}%", name_like));

            count_builder.push(" and ac.class_name like ");
            count_builder.push_bind(format!("%{}%", name_like));
        }
        if let Some(class_code) = class_code {
            builder.push(" and ac.code = ");
            builder.push_bind(class_code);

            count_builder.push(" and ac.code = ");
            count_builder.push_bind(class_code);
        }
        if let Some(is_active) = is_active {
            builder.push(" and ac.is_active = ");
            builder.push_bind(is_active);

            count_builder.push(" and ac.is_active = ");
            count_builder.push_bind(is_active);
        }
        builder.push(" order by ac.is_active desc , ac.created_at desc ");
        builder.push(" limit ");
        builder.push_bind(page_params.get_limit());
        builder.push(" offset ");
        builder.push_bind(page_params.get_offset());
        Ok((
            builder
                .build_query_as()
                .fetch_all(&self.db_pool)
                .await
                .map_err(|e| e.to_string())?
            ,
            count_builder
                .build_query_scalar()
                .fetch_one(&self.db_pool)
                .await
                .map_err(|e| e.to_string())?
            )
        )
    }


    /**
     * 作者：张瀚
     * 说明：查询自己担任班主任的班级列表
     */
    pub async fn find_head_teacher_class_list(
        &self,
        schema_name: &String,
        teacher_id: &Uuid,
    ) -> Result<Vec<AdministrativeClasses>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.administrative_classes ac where ac.teacher_id = ",
            schema_name
        ));
        builder.push_bind(teacher_id);
        builder.push(" order by ac.is_active desc , ac.created_at desc ");
        Ok(builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：查询班级内的学生的ID列表
     */
    pub async fn find_student_id_list_in_classes(
        &self,
        schema_name: &String,
        classes_id: &Uuid,
    ) -> Result<Vec<Uuid>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select s.id from {}.students s where s.administrative_class_id = ",
            schema_name
        ));
        builder.push_bind(classes_id);
        Ok(builder
            .build_query_scalar()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：查询指定编码的班级
     */
    pub async fn find_all_by_code(
        &self,
        schema_name: &String,
        code: &String,
    ) -> Result<Vec<AdministrativeClasses>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.administrative_classes ac where ac.code =  ",
            schema_name
        ));
        builder.push_bind(code);
        Ok(builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    pub async fn find_active_class_by_code(
        &self,
        schema_name: &String,
        code: &String,
    ) -> Result<Option<AdministrativeClasses>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.administrative_classes ac where ac.is_active = true and ac.code =  ",
            schema_name
        ));
        builder.push_bind(code);
        Ok(builder
            .build_query_as()
            .fetch_optional(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：获取行政班统计数据
     */
    pub async fn get_statistics(
        &self,
        context: &AuthContext,
        tenant_id: &Option<Uuid>,
        schema_name: &String,
        user_id: &Uuid,
    ) -> Result<AdministrativeClassesStatistics, String> {
        //获取能查看的班级列表
        let class_list = match context.is_admin_in_tenant(tenant_id.clone()) {
            true => self.get_all_class_list(schema_name).await?,
            false => self.get_user_class_list(schema_name, user_id).await?,
        };
        //统计班主任ID
        let mut teacher_id_set = HashSet::<Uuid>::new();
        let mut student_id_set = HashSet::<String>::new();
        for classes in &class_list {
            if classes.teacher_id.is_some() {
                teacher_id_set.insert(classes.teacher_id.clone().unwrap());
            }
            //查询班内学生ID列表
            let student_id_list = self
                .find_student_id_list_in_classes(schema_name, &classes.id)
                .await?;
            student_id_list.iter().for_each(|student_id| {
                student_id_set.insert(student_id.clone().to_string());
            });
        }
        let data = AdministrativeClassesStatistics {
            total_classes: class_list.len() as i32,
            total_teacher: teacher_id_set.len() as i32,
            total_students: student_id_set.len() as i32,
        };
        Ok(data)
    }
    /**
     * 作者：张瀚
     * 说明：创建行政班
     */
    pub async fn create_classes(
        &self,
        schema_name: &String,
        _user_id: &Uuid,
        params: &CreateAdministrativeClassesParams,
    ) -> Result<AdministrativeClasses> {
        //创建班级
        let CreateAdministrativeClassesParams {
            class_name,
            code,
            academic_year,
            grade_level_code,
            teacher_id,
        } = params;
        let mut tx = self.db_pool.begin().await?;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "INSERT INTO {}.administrative_classes (class_name,code,academic_year,grade_level_code,teacher_id) VALUES (",
            schema_name
        ));
        builder
            .push_bind(class_name)
            .push(" , ")
            .push_bind(code)
            .push(" , ")
            .push_bind(academic_year)
            .push(" , ")
            .push_bind(grade_level_code)
            .push(" , ")
            .push_bind(teacher_id)
            .push(" ) returning *");
        let class: AdministrativeClasses = builder
            .build_query_as()
            .fetch_one(&mut *tx)
            .await?;

        // Step 2: 查 teacher.user_id
        let user_id: Option<Uuid> = sqlx::query_scalar(&format!(
            "SELECT user_id FROM {}.teachers WHERE id = $1", schema_name
        ))
            .bind(teacher_id)
            .fetch_optional(&mut *tx)
            .await?;

        if let Some(uid) = user_id {
            // Step 3: 插入 user_identities
           let role_id:Option<Uuid>= sqlx::query_scalar("SELECT id FROM roles WHERE code = 'class_teacher'").fetch_optional(&mut *tx).await?;
            if role_id.is_none() {
                // 这里可以返回 Err 或者默认值
                bail!("创建班级时查找角色身份失败");
            }

            let _new_identity: UserIdentity = sqlx::query_as(&format!(
                "INSERT INTO {}.user_identities (user_id, role_id, target_type, target_id)
             VALUES ($1, $2, 'class', $3)
             RETURNING *", schema_name
            ))
                .bind(uid)
                .bind(role_id.unwrap())
                .bind(class.id)
                .fetch_one(&mut *tx)
                .await?;
        }

        tx.commit().await?;

        Ok(class)
    }

    /**
     * 作者：张瀚
     * 说明：查询行政班内所有学生信息
     */
    pub async fn find_all_student_in_class(
        &self,
        schema_name: &String,
        params: &FindAllStudentInClassParams,
    ) -> Result<Vec<Student>, String> {
        let FindAllStudentInClassParams { class_id } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select s.* from {}.students s where s.administrative_class_id = ",
            schema_name
        ));
        builder
            .push_bind(class_id)
            .push(" order by s.created_at desc ");
        builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：编辑班级
     */
    pub async fn update_classes(
        &self,
        schema_name: &String,
        params: &UpdateAdministrativeClassesParams,
    ) -> Result<()> {
        let UpdateAdministrativeClassesParams {
            id,
            class_name,
            code,
            academic_year,
            grade_level_code,
            teacher_id,
            is_active,
        } = params;

        let mut tx = self.db_pool.begin().await?;

        // 先获取原有 user_identity 的 user_id
        let old_user_id_opt: Option<Uuid> = sqlx::query_scalar(&format!(
            "SELECT user_id FROM {}.user_identities WHERE target_type='class' AND target_id=$1",
            schema_name
        ))
            .bind(id)
            .fetch_optional(&mut *tx)
            .await?;

        let mut builder = sqlx::QueryBuilder::new(format!(
            "update {}.administrative_classes ac set ",
            schema_name
        ));
        builder
            .push("class_name = ")
            .push_bind(class_name)
            .push(" , code = ")
            .push_bind(code)
            .push(" , academic_year = ")
            .push_bind(academic_year)
            .push(" , grade_level_code = ")
            .push_bind(grade_level_code)
            .push(" , teacher_id = ")
            .push_bind(teacher_id)
            .push(" , is_active = ")
            .push_bind(is_active)
            .push(" , updated_at = now() where id = ")
            .push_bind(id);
        builder.build()
            .execute(&mut *tx)
            .await?;

        // 查新 teacher 的 user_id
        let user_id_opt: Option<Uuid> = sqlx::query_scalar(&format!(
            "SELECT user_id FROM {}.teachers WHERE id = $1",
            schema_name
        ))
            .bind(teacher_id)
            .fetch_optional(&mut *tx)
            .await?;

        match user_id_opt {
            Some(user_id) => {
                // user_id存在 → 更新原有 user_identity
                sqlx::query(&format!(
                    "UPDATE {}.user_identities SET user_id = $1, updated_at = now()
                 WHERE target_type = 'class' AND target_id = $2",
                    schema_name
                ))
                    .bind(user_id)
                    .bind(id)
                    .execute(&mut *tx)
                    .await?;
                }
            None => {
                // user_id不存在 → 删除原有 user_identity
                sqlx::query(&format!(
                    "DELETE FROM {}.user_identities WHERE target_type = 'class' AND target_id = $1",
                    schema_name
                ))
                    .bind(id)
                    .execute(&mut *tx)
                    .await?;
            }
        }

        // 检查旧 user_id 是否还有其他 user_identities
        if let Some(old_user_id) = old_user_id_opt {
            let count: i64 = sqlx::query_scalar(
                &format!("SELECT COUNT(*) FROM {}.user_identities WHERE user_id = $1",
                    schema_name
                ))
                .bind(old_user_id)
                .fetch_one(&mut *tx)
                .await?;

            if count == 0 {
                sqlx::query(
                    "DELETE FROM public.user_tenant_links
                            WHERE user_id = $1
                        AND tenant_id = (SELECT id FROM public.tenants WHERE schema_name = $2)"
                )
                    .bind(old_user_id)
                    .bind(schema_name)
                    .execute(&mut *tx)
                    .await?;
            }
        }

        tx.commit().await?;

        Ok(())
    }

    /**
     * 作者：张瀚
     * 说明：把学生移动到行政班内
     */
    pub async fn move_student_to_administrative_classes(
        &self,
        schema_name: &String,
        params: &MoveStudentToAdministrativeClassesParams,
    ) -> Result<(), String> {
        let MoveStudentToAdministrativeClassesParams {
            class_id,
            student_id,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "update {}.students set administrative_class_id = ",
            schema_name
        ));
        builder
            .push_bind(class_id)
            .push(" where id = ")
            .push_bind(student_id);
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }

    /**
     * 作者：张瀚
     * 说明：把学生从行政班中移除
     */
    pub async fn remove_student_from_administrative_classes(
        &self,
        schema_name: &String,
        params: &RemoveStudentFromAdministrativeClassesParams,
    ) -> Result<(), String> {
        let RemoveStudentFromAdministrativeClassesParams {
            class_id,
            student_id,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "update {}.students set administrative_class_id = null ",
            schema_name
        ));
        builder
            .push(" where id = ")
            .push_bind(student_id)
            .push(" and administrative_class_id = ")
            .push_bind(class_id);
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }

    /**
     * 作者：张瀚
     * 说明：删除班级
     */
    pub async fn delete_class(
        &self,
        schema_name: &String,
        params: &DeleteAdministrativeClassesParams,
    ) -> Result<(), String> {
        let DeleteAdministrativeClassesParams { class_id } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "update {}.administrative_classes set is_active = false where id = ",
            schema_name
        ));
        builder.push_bind(class_id);
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }
    /**
     * 作者：朱若彪
     * 说明：分页查询班级内的学生
     */
    pub async fn page_student_in_class(
        &self,
        _context: &AuthContext,
        schema_name: &String,
        params: &PageStudentInClassParams,
    )->Result<(Vec<Student>,i64), String>{
        let PageStudentInClassParams{class_id,page_params,name_like,status,student_number}=params;
        let mut conn=connect_with_schema(&self.db_pool, &schema_name)
            .await
            .map_err(|e| e.to_string())?;
        //查询学生列表
        let (list,total)=StudentsRepository::page_student_in_class(&mut conn,class_id,page_params,name_like,status,student_number)
            .await
            .map_err(|e| e.to_string())?;
        Ok((list,total))
    }
}
