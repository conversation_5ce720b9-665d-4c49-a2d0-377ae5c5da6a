use std::collections::HashMap;
use std::sync::Arc;
use anyhow::Result;
use sqlx::{PgPool, QueryBuilder, Postgres};
use tracing::{debug, info};
use super::base::*;
use super::casbin_based::CasbinBasedDataFilter;
use super::casbin_query::CasbinQueryHelper;
use super::{ExamDataFilter, HomeworkDataFilter, GradeDataFilter, StudentDataFilter};
use super::super::{CasbinPermissionService, PermissionHierarchyResolver};
use crate::service::permission::casbin_service::MultiTenantCasbinService;

/// 数据过滤器管理器
pub struct DataFilterManager {
    filters: HashMap<String, Arc<dyn DataFilter>>,
}

impl Default for DataFilterManager {
    fn default() -> Self {
        Self::new()
    }
}
impl DataFilterManager {
    pub fn new() -> Self {
        Self {
            filters: HashMap::new(),
        }
    }
    
    /// 创建默认的数据过滤器管理器
    pub fn create_default(db_pool: PgPool) -> Self {
        let mut manager = Self::new();

        // 注册基于Casbin策略的数据过滤器
        let casbin_filter = Arc::new(CasbinBasedDataFilter::new(db_pool.clone()));
        manager.register_filter("administrative_class".to_string(), casbin_filter.clone());
        manager.register_filter("teacher".to_string(), casbin_filter.clone());
        manager.register_filter("homework".to_string(), casbin_filter.clone());

        manager
    }

    /// 创建增强的数据过滤器管理器（包含业务特定过滤器）
    pub fn create_enhanced(
        db_pool: PgPool,
        casbin_service: Arc<MultiTenantCasbinService>,
    ) -> Self {
        let mut manager = Self::new();

        // 创建共享组件
        let query_helper = CasbinQueryHelper::new(db_pool.clone());
        let hierarchy_resolver = Arc::new(PermissionHierarchyResolver::new(
            db_pool.clone(),
            casbin_service.clone()
        ));

        // 注册业务特定的数据过滤器
        manager.register_filter(
            "exam".to_string(),
            Arc::new(ExamDataFilter::new(query_helper.clone(), hierarchy_resolver.clone()))
        );

        manager.register_filter(
            "homework".to_string(),
            Arc::new(HomeworkDataFilter::new(query_helper.clone(), hierarchy_resolver.clone()))
        );

        manager.register_filter(
            "grade".to_string(),
            Arc::new(GradeDataFilter::new(query_helper.clone(), hierarchy_resolver.clone()))
        );

        manager.register_filter(
            "student".to_string(),
            Arc::new(StudentDataFilter::new(query_helper.clone(), hierarchy_resolver.clone()))
        );

        // 注册基于Casbin策略的数据过滤器（用于其他资源）
        let casbin_filter = Arc::new(CasbinBasedDataFilter::new(db_pool.clone()));
        manager.register_filter("administrative_class".to_string(), casbin_filter.clone());
        manager.register_filter("teacher".to_string(), casbin_filter.clone());
        manager.register_filter("class".to_string(), casbin_filter.clone());

        info!("Enhanced data filter manager created with {} filters", manager.filters.len());
        manager
    }
    
    /// 注册数据过滤器
    pub fn register_filter(&mut self, resource: String, filter: Arc<dyn DataFilter>) {
        self.filters.insert(resource, filter);
    }
    
    /// 获取数据过滤器
    pub fn get_filter(&self, resource: &str) -> Option<&Arc<dyn DataFilter>> {
        self.filters.get(resource)
    }
    
    /// 应用数据过滤
    pub async fn apply_data_filter<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<bool> {
        if let Some(filter) = self.get_filter(&context.resource) {
            info!("Applying data filter for resource: {}", context.resource);
            // 直接在QueryBuilder上应用过滤条件
            return filter.apply_filter_to_builders(context, query_builder, count_builder, casbin_service, table_alias).await;
        }

        info!("No data filter applied for resource: {}", context.resource);
        Ok(false)
    }

    /// 应用数据过滤
    pub async fn apply_data_query_filter<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<bool> {
        if let Some(filter) = self.get_filter(&context.resource) {
            info!("Applying data filter for resource: {}", context.resource);
            // 直接在QueryBuilder上应用过滤条件
            return filter.apply_filter_to_query_builders(context, query_builder, casbin_service, table_alias).await;
        }

        info!("No data filter applied for resource: {}", context.resource);
        Ok(false)
    }
}

/// 数据过滤器配置
#[derive(Debug, Clone)]
pub struct DataFilterConfig {
    /// 是否优先使用Casbin策略
    pub prefer_casbin: bool,
    /// 是否启用缓存
    pub enable_cache: bool,
    /// 缓存过期时间（秒）
    pub cache_ttl: u64,
    /// 是否启用调试日志
    pub debug_logging: bool,
}

impl Default for DataFilterConfig {
    fn default() -> Self {
        Self {
            prefer_casbin: true,
            enable_cache: true,
            cache_ttl: 300, // 5分钟
            debug_logging: false,
        }
    }
}

/// 增强的数据过滤器管理器
pub struct EnhancedDataFilterManager {
    filters: HashMap<String, Arc<dyn DataFilter>>,
    config: DataFilterConfig,
    db_pool: PgPool,
}

impl EnhancedDataFilterManager {
    pub fn new(db_pool: PgPool, config: DataFilterConfig) -> Self {
        Self {
            filters: HashMap::new(),
            config,
            db_pool,
        }
    }

    /// 注册自定义过滤器
    pub fn register_filter(&mut self, resource: String, filter: Arc<dyn DataFilter>) {
        self.filters.insert(resource, filter);
    }

    /// 获取过滤器
    pub fn get_filter(&self, resource: &str) -> Option<&Arc<dyn DataFilter>> {
        self.filters.get(resource)
    }

    /// 应用数据过滤
    pub async fn apply_data_filter<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
        table_alias: &str,
    ) -> Result<bool> {
        if let Some(filter) = self.get_filter(&context.resource) {
            let start_time = std::time::Instant::now();
            
            let result = filter.apply_filter_to_builders(
                context, 
                query_builder, 
                count_builder, 
                casbin_service,
                table_alias
            ).await;

            if self.config.debug_logging {
                let duration = start_time.elapsed();
                info!(
                    "Data filter applied for resource '{}' in {:?} for user {}",
                    context.resource, duration, context.user_id
                );
            }

            return result;
        }

        debug!("No data filter found for resource: {}", context.resource);
        Ok(false)
    }

    /// 更新配置
    pub fn update_config(&mut self, config: DataFilterConfig) {
        self.config = config;
        // 重新初始化过滤器以应用新配置
        self.filters.clear();
    }
}