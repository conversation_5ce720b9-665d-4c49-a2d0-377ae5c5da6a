#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;
    use uuid::Uuid;
    use sqlx::PgPool;
    use crate::service::student::student_service::StudentService;
    use crate::service::user::user_service::UserService;
    use crate::service::tenant::user::tenant_user_service::TenantUserService;
    use crate::service::tenant::tenant_service::TenantService;
    use crate::service::role::role_service::RoleService;
    use crate::service::administrative_classes::administrative_classes_service::AdministrativeClassesService;

    // 这是一个示例测试，展示如何测试并行导入功能
    // 实际测试需要数据库连接和完整的服务设置
    #[tokio::test]
    async fn test_parallel_import_performance() {
        // 注意：这个测试需要实际的数据库连接才能运行
        // 这里只是展示测试结构
        
        // 模拟创建服务实例（实际测试中需要真实的数据库连接）
        // let db_pool = create_test_db_pool().await;
        // let student_service = Arc::new(StudentService::new(db_pool.clone()));
        // let user_service = UserService::new(db_pool.clone());
        // let tenant_user_service = Arc::new(TenantUserService::new(db_pool.clone()));
        // let tenant_service = TenantService::new(db_pool.clone());
        // let role_service = RoleService::new(db_pool.clone());
        // let administrative_classes_service = Arc::new(AdministrativeClassesService::new(db_pool.clone()));
        
        // let import_service = StudentImportService::new(
        //     db_pool,
        //     student_service,
        //     user_service,
        //     tenant_user_service,
        //     tenant_service,
        //     role_service,
        //     administrative_classes_service,
        // );

        // 测试不同并发级别的性能
        // let test_data = create_test_excel_data();
        // let schema_name = "test_schema";
        // let user_id = Uuid::new_v4();

        // 测试串行处理（并发数为1）
        // let start_time = std::time::Instant::now();
        // let result_serial = import_service
        //     .import_students_with_concurrency(schema_name, &test_data, user_id, 1)
        //     .await;
        // let serial_duration = start_time.elapsed();

        // 测试并行处理（并发数为10）
        // let start_time = std::time::Instant::now();
        // let result_parallel = import_service
        //     .import_students_with_concurrency(schema_name, &test_data, user_id, 10)
        //     .await;
        // let parallel_duration = start_time.elapsed();

        // 验证结果一致性
        // assert_eq!(result_serial.unwrap().success, result_parallel.unwrap().success);
        
        // 验证并行处理更快（在有足够数据的情况下）
        // println!("Serial duration: {:?}", serial_duration);
        // println!("Parallel duration: {:?}", parallel_duration);
        
        // 在实际测试中，并行处理应该更快
        // assert!(parallel_duration < serial_duration);
        
        println!("Parallel import test structure created successfully");
    }

    #[test]
    fn test_max_concurrent_imports_constant() {
        // 测试常量值是否合理
        assert_eq!(MAX_CONCURRENT_IMPORTS, 10);
        assert!(MAX_CONCURRENT_IMPORTS > 0);
        assert!(MAX_CONCURRENT_IMPORTS <= 50); // 避免过度并发
    }

    #[test]
    fn test_parse_student_row_performance() {
        // 测试行解析性能（这个可以不需要数据库连接）
        use calamine::Data;
        
        // 创建模拟的Excel行数据
        let row_data = vec![
            Data::Int(1),                           // 序号
            Data::String("20240001".to_string()),   // 学号
            Data::String("张三".to_string()),        // 姓名
            Data::String("901".to_string()),        // 班级代码
            Data::String("九年级".to_string()),       // 年级
            Data::String("2008-01-01".to_string()), // 出生日期
            Data::String("123456789012345678".to_string()), // 身份证号
            Data::String("13800138000".to_string()), // 电话
            Data::String("<EMAIL>".to_string()), // 邮箱
            Data::String("测试地址".to_string()),      // 地址
            Data::String("张父".to_string()),         // 监护人姓名
            Data::String("13900139000".to_string()), // 监护人电话
            Data::String("父亲".to_string()),         // 监护人关系
        ];

        // 创建一个临时的导入服务实例用于测试解析功能
        // 注意：这里我们只测试解析功能，不需要完整的服务依赖
        let db_pool = sqlx::PgPool::connect("postgresql://dummy").await.unwrap_or_else(|_| {
            // 如果连接失败，跳过这个测试
            return;
        });
        
        // 由于我们只测试解析功能，可以创建一个最小化的服务实例
        // 但实际上parse_student_row是一个纯函数，不依赖外部状态
        println!("Row parsing test would go here - requires service instance");
    }
}

// 辅助函数用于创建测试数据
#[cfg(test)]
fn create_test_excel_data() -> Vec<u8> {
    // 这里应该创建一个包含测试学生数据的Excel文件
    // 实际实现中可以使用rust_xlsxwriter或类似库创建测试数据
    vec![]
}
