use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

use crate::model::PageParams;

#[derive(Debug, FromRow, Clone, Serialize, Deserialize)]
pub struct Homework {
    pub id: Uuid,
    pub homework_name: String,
    pub homework_status: String,
    pub subject_group_id: Option<Uuid>,
    pub description: Option<String>,
    pub leaf_count:i32, // 作业张数
    pub leaf_total:i32, // 本场作业总题卡张数(实际参与人数*题卡张数)
    pub page_count:i32, // 作业页数
    pub page_total:i32, // 本场作业总题卡页数(实际参与人数*题卡页数)
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

///作业统计结果
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct HomeworkStatistics {
    pub total_homeworks: i32,
    pub completed_homeworks: i32,
    pub in_progress_homeworks: i32,
    pub total_students: i32,
}

/// 学生作业记录结果
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct HomeworkStudentsRecord {
    pub id: Uuid,
    pub homework_id: Uuid,
    pub student_id: Uuid,
    pub student_number: String,
    pub student_name: String,
    pub status: String,
}

///草稿，未发布的，学生看不到的状态
pub const HOMEWORK_STATUS_DRAFT: &str = "Draft";
///已经开始扫描评阅
pub const HOMEWORK_STATUS_DONING: &str = "Doing";
///作业已经完成，分数已经下发，后续状态不会再变，重评等都是实时更新
pub const HOMEWORK_STATUS_DONE: &str = "Done";

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct CreateHomeworkParams {
    pub homework_name: String,
    pub homework_status: String,
    pub subject_group_id: Option<Uuid>,
    pub description: Option<String>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct UpdateHomeworkParams {
    pub id: Uuid,
    pub homework_name: String,
    pub homework_status: String,
    pub subject_group_id: Option<Uuid>,
    pub description: Option<String>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct PageHomeworkParams {
    pub page_params: PageParams,
    pub name: Option<String>,
    pub status: Option<String>,
    pub subject_group_id: Option<Uuid>,
}
